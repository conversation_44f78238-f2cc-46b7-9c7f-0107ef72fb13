[{"name": "hot-reloader", "duration": 70, "timestamp": 42133314495, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748449764063, "traceId": "a2f9299be4c8ba45"}, {"name": "setup-dev-bundler", "duration": 1394072, "timestamp": 42132261593, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748449763010, "traceId": "a2f9299be4c8ba45"}, {"name": "run-instrumentation-hook", "duration": 22, "timestamp": 42133689945, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748449764438, "traceId": "a2f9299be4c8ba45"}, {"name": "start-dev-server", "duration": 1834197, "timestamp": 42131869858, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "48463687680", "memory.totalMem": "68560273408", "memory.heapSizeLimit": "34481373184", "memory.rss": "170139648", "memory.heapTotal": "111906816", "memory.heapUsed": "57602880"}, "startTime": 1748449762618, "traceId": "a2f9299be4c8ba45"}, {"name": "compile-path", "duration": 1345568, "timestamp": 42154498753, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748449785247, "traceId": "a2f9299be4c8ba45"}, {"name": "ensure-page", "duration": 1346426, "timestamp": 42154498263, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748449785247, "traceId": "a2f9299be4c8ba45"}]