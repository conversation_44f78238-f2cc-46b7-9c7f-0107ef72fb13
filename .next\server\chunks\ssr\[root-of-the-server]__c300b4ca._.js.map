{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_6d9032cf.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_6d9032cf-module__7XMLfa__className\",\n  \"variable\": \"inter_6d9032cf-module__7XMLfa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_6d9032cf.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_515fd822.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_515fd822-module__SRVNyW__className\",\n  \"variable\": \"jetbrains_mono_515fd822-module__SRVNyW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_515fd822.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22variable%22:%22--font-jetbrains-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/app/layout.js"], "sourcesContent": ["import { Inter, JetBrains_Mono } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  variable: \"--font-jetbrains-mono\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata = {\n  title: \"ReportU - Cross-Border Offense Reporting Platform\",\n  description: \"Simplify offense reporting across Malaysia and Singapore. Submit reports with multimedia evidence, get real-time updates, and ensure your voice is heard.\",\n  keywords: \"offense reporting, Malaysia, Singapore, crime reporting, traffic violations, government services\",\n  authors: [{ name: \"ReportU Team\" }],\n  creator: \"ReportU\",\n  publisher: \"ReportU\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL(\"https://reportu.app\"),\n  openGraph: {\n    title: \"ReportU - Cross-Border Offense Reporting Platform\",\n    description: \"Simplify offense reporting across Malaysia and Singapore with our AI-powered platform.\",\n    url: \"https://reportu.app\",\n    siteName: \"ReportU\",\n    images: [\n      {\n        url: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n        width: 1200,\n        height: 630,\n        alt: \"ReportU Platform\",\n      },\n    ],\n    locale: \"en_US\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"ReportU - Cross-Border Offense Reporting Platform\",\n    description: \"Simplify offense reporting across Malaysia and Singapore with our AI-powered platform.\",\n    images: [\"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\"],\n  },\n  icons: {\n    icon: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    shortcut: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    apple: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n  },\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <head>\n        <link rel=\"icon\" href=\"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\" />\n      </head>\n      <body\n        className={`${inter.variable} ${jetbrainsMono.variable} antialiased bg-gray-900 text-white overflow-x-hidden`}\n      >\n        <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\">\n          {children}\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAeO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAe;KAAE;IACnC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA4F;IACvG;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;0BACC,cAAA,8OAAC;oBAAK,KAAI;oBAAO,MAAK;;;;;;;;;;;0BAExB,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,qDAAqD,CAAC;0BAE7G,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}