{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'Why Us', href: '/why-us' },\n    { name: 'Pitch', href: '/pitch' },\n    { name: 'Roadmap', href: '/roadmap' },\n  ];\n\n  return (\n    <header \n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-gray-900/95 backdrop-blur-md border-b border-blue-500/20' \n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 hover-lift\">\n            <div className=\"relative\">\n              <div className=\"w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center glow-blue\">\n                <svg \n                  className=\"w-6 h-6 text-white\" \n                  fill=\"currentColor\" \n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z\"/>\n                  <path d=\"M9 12l2 2 4-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n                </svg>\n              </div>\n              <div className=\"absolute -inset-1 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-lg blur opacity-30 animate-pulse\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gradient\">ReportU</span>\n              <span className=\"text-xs text-gray-400 -mt-1\">Cross-Border Reporting</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-300 hover:text-cyan-400 transition-colors duration-200 font-medium relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-cyan-400 transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/demo\"\n              className=\"btn-primary hover-glow\"\n            >\n              Try Demo\n            </Link>\n            <Link\n              href=\"/signup\"\n              className=\"btn-secondary\"\n            >\n              Sign Up\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-300 hover:text-cyan-400 transition-colors duration-200 p-2\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-gray-800/95 backdrop-blur-md rounded-lg mt-2 border border-blue-500/20\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-gray-300 hover:text-cyan-400 hover:bg-gray-700/50 rounded-md transition-all duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 space-y-2\">\n                <Link\n                  href=\"/demo\"\n                  className=\"block w-full text-center btn-primary\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Try Demo\n                </Link>\n                <Link\n                  href=\"/signup\"\n                  className=\"block w-full text-center btn-secondary\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Sign Up\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QACC,WAAW,CAAC,4DAA4D,EACtE,aACI,gEACA,kBACJ;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;;kEAER,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;wDAAgB,QAAO;wDAAe,aAAY;wDAAI,MAAK;;;;;;;;;;;;;;;;;sDAGvE,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;sDAClD,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;uCAEe", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Demo', href: '/demo' },\n      { name: 'Features', href: '/#features' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n    ],\n    company: [\n      { name: 'About', href: '/why-us' },\n      { name: 'Pitch Deck', href: '/pitch' },\n      { name: 'Contact', href: '/contact' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'API Reference', href: '/api' },\n      { name: 'Status', href: '/status' },\n    ],\n  };\n\n  return (\n    <footer className=\"bg-gray-900/50 border-t border-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <div className=\"w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center glow-blue\">\n                <svg \n                  className=\"w-6 h-6 text-white\" \n                  fill=\"currentColor\" \n                  viewBox=\"0 0 24 24\"\n                >\n                  <path d=\"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z\"/>\n                  <path d=\"M9 12l2 2 4-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n                </svg>\n              </div>\n              <div>\n                <span className=\"text-xl font-bold text-gradient\">ReportU</span>\n                <p className=\"text-sm text-gray-400\">Cross-Border Reporting Platform</p>\n              </div>\n            </div>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Simplifying offense reporting across Malaysia and Singapore with AI-powered routing and real-time updates.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200\">\n                <span className=\"sr-only\">Facebook</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200\">\n                <span className=\"sr-only\">Twitter</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200\">\n                <span className=\"sr-only\">LinkedIn</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M19 0H5a5 5 0 00-5 5v14a5 5 0 005 5h14a5 5 0 005-5V5a5 5 0 00-5-5zM8 19H5V8h3v11zM6.5 6.732c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zM20 19h-3v-5.604c0-3.368-4-3.113-4 0V19h-3V8h3v1.765c1.396-2.586 7-2.777 7 2.476V19z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Links Sections */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4\">Product</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link href={link.href} className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200\">\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4\">Company</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link href={link.href} className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200\">\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 tracking-wider uppercase mb-4\">Support</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link href={link.href} className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200\">\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"mt-12 pt-8 border-t border-gray-800\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 text-sm\">\n              © {currentYear} ReportU. All rights reserved.\n            </p>\n            <div className=\"flex space-x-6 mt-4 md:mt-0\">\n              {footerLinks.legal.map((link) => (\n                <Link \n                  key={link.name} \n                  href={link.href} \n                  className=\"text-gray-400 hover:text-cyan-400 transition-colors duration-200 text-sm\"\n                >\n                  {link.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,MAAM;YAAU;YACjC;gBAAE,MAAM;gBAAc,MAAM;YAAS;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAiB,MAAM;YAAO;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;;kEAER,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;wDAAgB,QAAO;wDAAe,aAAY;wDAAI,MAAK;;;;;;;;;;;;;;;;;sDAGvE,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;;sDAGjT,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8Q,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO1T,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAClF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,IAAI;gDAAE,WAAU;0DAC9B,KAAK,IAAI;;;;;;2CAFL,KAAK,IAAI;;;;;;;;;;;;;;;;sCASxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAClF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,IAAI;gDAAE,WAAU;0DAC9B,KAAK,IAAI;;;;;;2CAFL,KAAK,IAAI;;;;;;;;;;;;;;;;sCASxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAClF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,KAAK,IAAI;gDAAE,WAAU;0DAC9B,KAAK,IAAI;;;;;;2CAFL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAU1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAwB;oCAChC;oCAAY;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAahC;uCAEe", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/lib/constants.js"], "sourcesContent": ["// ReportU Constants and Data\n\nexport const REPORT_CATEGORIES = {\n  traffic: {\n    id: 'traffic',\n    name: 'Traffic Violations',\n    icon: '🚗',\n    description: 'Illegal parking, speeding, reckless driving',\n    departments: {\n      malaysia: ['JPJ', 'PDRM'],\n      singapore: ['LTA', 'TP']\n    },\n    priority: 'medium',\n    estimatedTime: '3-5 days',\n    color: 'from-orange-500 to-red-500'\n  },\n  crime: {\n    id: 'crime',\n    name: 'Criminal Activities',\n    icon: '🚨',\n    description: 'Theft, vandalism, suspicious activities',\n    departments: {\n      malaysia: ['PDRM'],\n      singapore: ['SPF']\n    },\n    priority: 'high',\n    estimatedTime: '1-2 days',\n    color: 'from-red-500 to-pink-500'\n  },\n  environment: {\n    id: 'environment',\n    name: 'Environmental Issues',\n    icon: '🌱',\n    description: 'Pollution, illegal dumping, noise complaints',\n    departments: {\n      malaysia: ['DOE', 'Local Council'],\n      singapore: ['NEA', 'PUB']\n    },\n    priority: 'low',\n    estimatedTime: '5-7 days',\n    color: 'from-green-500 to-emerald-500'\n  },\n  public: {\n    id: 'public',\n    name: 'Public Safety',\n    icon: '🏛️',\n    description: 'Infrastructure damage, public disturbances',\n    departments: {\n      malaysia: ['Local Council', 'PDRM'],\n      singapore: ['HDB', 'URA']\n    },\n    priority: 'medium',\n    estimatedTime: '3-5 days',\n    color: 'from-blue-500 to-cyan-500'\n  },\n  commercial: {\n    id: 'commercial',\n    name: 'Commercial Violations',\n    icon: '🏪',\n    description: 'Counterfeit goods, illegal business operations',\n    departments: {\n      malaysia: ['KPDNHEP', 'Local Council'],\n      singapore: ['SPRING', 'IE Singapore']\n    },\n    priority: 'medium',\n    estimatedTime: '5-10 days',\n    color: 'from-purple-500 to-indigo-500'\n  }\n};\n\nexport const REPORT_STATUSES = [\n  {\n    id: 'submitted',\n    name: 'Submitted',\n    description: 'Report received and logged',\n    icon: '📝',\n    color: 'text-blue-400'\n  },\n  {\n    id: 'processing',\n    name: 'Processing',\n    description: 'Under initial review',\n    icon: '⚙️',\n    color: 'text-yellow-400'\n  },\n  {\n    id: 'investigating',\n    name: 'Investigating',\n    description: 'Investigation in progress',\n    icon: '🔍',\n    color: 'text-orange-400'\n  },\n  {\n    id: 'resolved',\n    name: 'Resolved',\n    description: 'Case closed successfully',\n    icon: '✅',\n    color: 'text-green-400'\n  },\n  {\n    id: 'rejected',\n    name: 'Rejected',\n    description: 'Report rejected or invalid',\n    icon: '❌',\n    color: 'text-red-400'\n  }\n];\n\nexport const COUNTRIES = {\n  malaysia: {\n    id: 'malaysia',\n    name: 'Malaysia',\n    flag: '🇲🇾',\n    code: 'MY',\n    timezone: 'Asia/Kuala_Lumpur',\n    currency: 'MYR',\n    languages: ['English', 'Malay', 'Mandarin', 'Tamil']\n  },\n  singapore: {\n    id: 'singapore',\n    name: 'Singapore',\n    flag: '🇸🇬',\n    code: 'SG',\n    timezone: 'Asia/Singapore',\n    currency: 'SGD',\n    languages: ['English', 'Malay', 'Mandarin', 'Tamil']\n  }\n};\n\nexport const FEATURES = [\n  {\n    id: 'smart-routing',\n    title: 'Smart AI Routing',\n    description: 'Automatically routes reports to the correct department using AI categorization',\n    icon: '🤖',\n    benefits: ['99% accuracy', 'Instant routing', 'No manual sorting']\n  },\n  {\n    id: 'real-time-tracking',\n    title: 'Real-Time Tracking',\n    description: 'Track your report status from submission to resolution',\n    icon: '📊',\n    benefits: ['Live updates', 'Push notifications', 'Timeline view']\n  },\n  {\n    id: 'multimedia-evidence',\n    title: 'Multimedia Evidence',\n    description: 'Upload photos, videos, and documents as evidence',\n    icon: '📸',\n    benefits: ['Multiple formats', 'Cloud storage', 'Secure handling']\n  },\n  {\n    id: 'cross-border',\n    title: 'Cross-Border Support',\n    description: 'Seamless reporting across Malaysia and Singapore',\n    icon: '🌏',\n    benefits: ['Unified platform', 'Auto-detection', 'Local compliance']\n  },\n  {\n    id: 'anonymous-reporting',\n    title: 'Anonymous Reporting',\n    description: 'Submit reports anonymously for sensitive cases',\n    icon: '🔒',\n    benefits: ['Privacy protection', 'Secure submission', 'No personal data']\n  },\n  {\n    id: 'multi-language',\n    title: 'Multi-Language',\n    description: 'Available in English, Malay, Mandarin, and Tamil',\n    icon: '🌐',\n    benefits: ['4 languages', 'Auto-translation', 'Cultural adaptation']\n  }\n];\n\nexport const TESTIMONIALS = [\n  {\n    id: 1,\n    name: 'Ahmad Rahman',\n    role: 'Kuala Lumpur Resident',\n    avatar: '👨🏽‍💼',\n    content: 'ReportU made reporting a traffic violation so easy. I got updates throughout the process and the issue was resolved quickly.',\n    rating: 5,\n    location: 'Malaysia'\n  },\n  {\n    id: 2,\n    name: 'Sarah Lim',\n    role: 'Singapore Citizen',\n    avatar: '👩🏻‍💻',\n    content: 'Finally, a platform that works across borders! Reported illegal dumping during my trip to Malaysia seamlessly.',\n    rating: 5,\n    location: 'Singapore'\n  },\n  {\n    id: 3,\n    name: 'Raj Patel',\n    role: 'Business Owner',\n    avatar: '👨🏾‍💼',\n    content: 'The AI routing is incredible. My counterfeit goods report went straight to the right department without any confusion.',\n    rating: 5,\n    location: 'Malaysia'\n  },\n  {\n    id: 4,\n    name: 'Michelle Tan',\n    role: 'Tourist',\n    avatar: '👩🏻‍🦱',\n    content: 'As a tourist, I was worried about reporting an incident. ReportU made it simple and I felt heard throughout the process.',\n    rating: 5,\n    location: 'Singapore'\n  }\n];\n\nexport const PRICING_TIERS = [\n  {\n    id: 'citizen',\n    name: 'Citizens',\n    price: 'Free',\n    description: 'For individual citizens reporting offenses',\n    features: [\n      'Unlimited report submissions',\n      'Real-time status tracking',\n      'Multimedia evidence upload',\n      'Cross-border support',\n      'Multi-language interface',\n      'Anonymous reporting option'\n    ],\n    cta: 'Start Reporting',\n    popular: false,\n    color: 'from-blue-500 to-cyan-500'\n  },\n  {\n    id: 'government',\n    name: 'Government',\n    price: '$25,000',\n    period: '/department/year',\n    description: 'For government departments and agencies',\n    features: [\n      'Department dashboard',\n      'Report management system',\n      'Analytics and insights',\n      'API integration',\n      'Priority support',\n      'Custom workflows',\n      'Bulk processing tools',\n      'Advanced reporting'\n    ],\n    cta: 'Contact Sales',\n    popular: true,\n    color: 'from-purple-500 to-indigo-500'\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    price: '$150,000',\n    period: '/organization/year',\n    description: 'For large organizations and enterprises',\n    features: [\n      'Everything in Government',\n      'White-label solution',\n      'Custom integrations',\n      'Dedicated support',\n      'SLA guarantees',\n      'Advanced security',\n      'Custom training',\n      'Multi-tenant support'\n    ],\n    cta: 'Contact Sales',\n    popular: false,\n    color: 'from-green-500 to-emerald-500'\n  }\n];\n\nexport const STATS = [\n  {\n    id: 'reports',\n    value: '50,000+',\n    label: 'Reports Processed',\n    icon: '📊'\n  },\n  {\n    id: 'users',\n    value: '25,000+',\n    label: 'Active Users',\n    icon: '👥'\n  },\n  {\n    id: 'departments',\n    value: '150+',\n    label: 'Partner Departments',\n    icon: '🏛️'\n  },\n  {\n    id: 'resolution',\n    value: '85%',\n    label: 'Resolution Rate',\n    icon: '✅'\n  }\n];\n\nexport const ROADMAP_ITEMS = [\n  {\n    id: 'q1-2024',\n    quarter: 'Q1 2024',\n    title: 'MVP Launch',\n    status: 'completed',\n    items: [\n      'Core reporting functionality',\n      'Malaysia & Singapore integration',\n      'Basic mobile app',\n      'AI routing system'\n    ]\n  },\n  {\n    id: 'q2-2024',\n    quarter: 'Q2 2024',\n    title: 'Enhanced Features',\n    status: 'in-progress',\n    items: [\n      'Real-time notifications',\n      'Advanced analytics',\n      'Government partnerships',\n      'Community features'\n    ]\n  },\n  {\n    id: 'q3-2024',\n    quarter: 'Q3 2024',\n    title: 'Scale & Expand',\n    status: 'planned',\n    items: [\n      'Additional ASEAN countries',\n      'Enterprise features',\n      'API marketplace',\n      'Advanced AI capabilities'\n    ]\n  },\n  {\n    id: 'q4-2024',\n    quarter: 'Q4 2024',\n    title: 'Global Platform',\n    status: 'planned',\n    items: [\n      'Global expansion',\n      'Blockchain integration',\n      'IoT sensor integration',\n      'Predictive analytics'\n    ]\n  }\n];\n\nexport const DEMO_SCENARIOS = [\n  {\n    id: 'traffic-violation',\n    title: 'Traffic Violation Report',\n    description: 'Report illegal parking in Kuala Lumpur',\n    category: 'traffic',\n    location: 'Kuala Lumpur, Malaysia',\n    evidence: ['photo1.jpg', 'video1.mp4'],\n    status: 'investigating'\n  },\n  {\n    id: 'environmental-issue',\n    title: 'Illegal Dumping',\n    description: 'Report illegal waste dumping in Singapore',\n    category: 'environment',\n    location: 'Singapore',\n    evidence: ['photo2.jpg'],\n    status: 'resolved'\n  },\n  {\n    id: 'public-safety',\n    title: 'Broken Infrastructure',\n    description: 'Report damaged streetlight in Johor',\n    category: 'public',\n    location: 'Johor Bahru, Malaysia',\n    evidence: ['photo3.jpg'],\n    status: 'processing'\n  }\n];\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;;;;;;AAEtB,MAAM,oBAAoB;IAC/B,SAAS;QACP,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;YACX,UAAU;gBAAC;gBAAO;aAAO;YACzB,WAAW;gBAAC;gBAAO;aAAK;QAC1B;QACA,UAAU;QACV,eAAe;QACf,OAAO;IACT;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;YACX,UAAU;gBAAC;aAAO;YAClB,WAAW;gBAAC;aAAM;QACpB;QACA,UAAU;QACV,eAAe;QACf,OAAO;IACT;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;YACX,UAAU;gBAAC;gBAAO;aAAgB;YAClC,WAAW;gBAAC;gBAAO;aAAM;QAC3B;QACA,UAAU;QACV,eAAe;QACf,OAAO;IACT;IACA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;YACX,UAAU;gBAAC;gBAAiB;aAAO;YACnC,WAAW;gBAAC;gBAAO;aAAM;QAC3B;QACA,UAAU;QACV,eAAe;QACf,OAAO;IACT;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;YACX,UAAU;gBAAC;gBAAW;aAAgB;YACtC,WAAW;gBAAC;gBAAU;aAAe;QACvC;QACA,UAAU;QACV,eAAe;QACf,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB;IAC7B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;AAEM,MAAM,YAAY;IACvB,UAAU;QACR,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;YAAC;YAAW;YAAS;YAAY;SAAQ;IACtD;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;YAAC;YAAW;YAAS;YAAY;SAAQ;IACtD;AACF;AAEO,MAAM,WAAW;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAgB;YAAmB;SAAoB;IACpE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAgB;YAAsB;SAAgB;IACnE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAoB;YAAiB;SAAkB;IACpE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAoB;YAAkB;SAAmB;IACtE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAsB;YAAqB;SAAmB;IAC3E;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YAAC;YAAe;YAAoB;SAAsB;IACtE;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,MAAM,gBAAgB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;QACT,OAAO;IACT;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD;AAEM,MAAM,gBAAgB;IAC3B;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;YACL;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;YACL;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;YACL;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;YACL;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;YAAC;YAAc;SAAa;QACtC,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;YAAC;SAAa;QACxB,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;YAAC;SAAa;QACxB,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-reportu/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\nimport { FEATURES, TESTIMONIALS, PRICING_TIERS, STATS } from '@/lib/constants';\n\nexport default function Home() {\n  const heroRef = useRef(null);\n  const particlesRef = useRef(null);\n\n  useEffect(() => {\n    // Initialize particle animation\n    if (typeof window !== 'undefined') {\n      initParticles();\n    }\n  }, []);\n\n  const initParticles = () => {\n    const canvas = particlesRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    canvas.width = window.innerWidth;\n    canvas.height = window.innerHeight;\n\n    const particles = [];\n    const particleCount = 50;\n\n    for (let i = 0; i < particleCount; i++) {\n      particles.push({\n        x: Math.random() * canvas.width,\n        y: Math.random() * canvas.height,\n        vx: (Math.random() - 0.5) * 0.5,\n        vy: (Math.random() - 0.5) * 0.5,\n        size: Math.random() * 2 + 1,\n        opacity: Math.random() * 0.5 + 0.2\n      });\n    }\n\n    function animate() {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      particles.forEach(particle => {\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;\n        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;\n\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fillStyle = `rgba(0, 255, 255, ${particle.opacity})`;\n        ctx.fill();\n      });\n\n      requestAnimationFrame(animate);\n    }\n\n    animate();\n\n    const handleResize = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n\n      {/* Hero Section */}\n      <section ref={heroRef} className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n        {/* Particle Background */}\n        <canvas\n          ref={particlesRef}\n          className=\"absolute inset-0 z-0\"\n          style={{ background: 'transparent' }}\n        />\n\n        {/* Gradient Overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-gray-900/90 via-blue-900/80 to-purple-900/90 z-10\"></div>\n\n        {/* Hero Content */}\n        <div className=\"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <div className=\"space-y-8 fade-in\">\n            {/* Main Headline */}\n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold leading-tight\">\n              <span className=\"text-gradient\">Cross-Border</span>\n              <br />\n              <span className=\"text-white\">Offense Reporting</span>\n              <br />\n              <span className=\"text-gradient-purple\">Made Simple</span>\n            </h1>\n\n            {/* Subtitle */}\n            <p className=\"text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n              Report offenses across <span className=\"text-cyan-400 font-semibold\">Malaysia</span> and{' '}\n              <span className=\"text-cyan-400 font-semibold\">Singapore</span> with AI-powered routing,\n              real-time updates, and seamless cross-border integration.\n            </p>\n\n            {/* Key Benefits */}\n            <div className=\"flex flex-wrap justify-center gap-4 text-sm md:text-base\">\n              <div className=\"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2\">\n                <span className=\"text-green-400\">✓</span>\n                <span>Instant AI Routing</span>\n              </div>\n              <div className=\"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2\">\n                <span className=\"text-green-400\">✓</span>\n                <span>Real-Time Updates</span>\n              </div>\n              <div className=\"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2\">\n                <span className=\"text-green-400\">✓</span>\n                <span>Cross-Border Support</span>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center pt-8\">\n              <Link\n                href=\"/demo\"\n                className=\"btn-primary text-lg px-8 py-4 hover-glow scale-in\"\n                style={{ animationDelay: '0.5s' }}\n              >\n                🚀 Try Live Demo\n              </Link>\n              <Link\n                href=\"#features\"\n                className=\"btn-secondary text-lg px-8 py-4 scale-in\"\n                style={{ animationDelay: '0.7s' }}\n              >\n                Learn More\n              </Link>\n            </div>\n\n            {/* Mini Demo Preview */}\n            <div className=\"pt-12 slide-in-up\" style={{ animationDelay: '1s' }}>\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 max-w-md mx-auto border border-cyan-500/20 glow-cyan\">\n                <h3 className=\"text-lg font-semibold mb-4 text-cyan-400\">Quick Report Preview</h3>\n                <div className=\"space-y-3 text-left\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full flex items-center justify-center text-sm\">1</div>\n                    <span className=\"text-gray-300\">Select offense type</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center text-sm\">2</div>\n                    <span className=\"text-gray-300\">Upload evidence</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-sm\">3</div>\n                    <span className=\"text-gray-300\">Get real-time updates</span>\n                  </div>\n                </div>\n                <div className=\"mt-4 text-center\">\n                  <span className=\"text-green-400 text-sm font-medium\">⚡ Average processing: 2-5 days</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n          <div className=\"animate-bounce\">\n            <svg className=\"w-6 h-6 text-cyan-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,uCAAmC;;QAEnC;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,MAAM,SAAS,aAAa,OAAO;QACnC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,OAAO,KAAK,GAAG,OAAO,UAAU;QAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QAElC,MAAM,YAAY,EAAE;QACpB,MAAM,gBAAgB;QAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,UAAU,IAAI,CAAC;gBACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;gBAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;gBAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;gBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;YACjC;QACF;QAEA,SAAS;YACP,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C,UAAU,OAAO,CAAC,CAAA;gBAChB,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,IAAI,SAAS,EAAE;gBAEzB,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC;gBACjE,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC;gBAElE,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;gBAC5D,IAAI,SAAS,GAAG,CAAC,kBAAkB,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;gBACxD,IAAI,IAAI;YACV;YAEA,sBAAsB;QACxB;QAEA;QAEA,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QACpC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qIAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,KAAK;gBAAS,WAAU;;kCAE/B,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,YAAY;wBAAc;;;;;;kCAIrC,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAAa;;;;;;sDAC7B,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;;8CAIzC,8OAAC;oCAAE,WAAU;;wCAAsE;sDAC1D,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAe;wCAAK;sDACzF,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAgB;;;;;;;8CAKhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAO;gDAAE,gBAAgB;4CAAO;sDACjC;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAO;gDAAE,gBAAgB;4CAAO;sDACjC;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;oCAAoB,OAAO;wCAAE,gBAAgB;oCAAK;8CAC/D,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA2G;;;;;;0EAC1H,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA6G;;;;;;0EAC5H,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA6G;;;;;;0EAC5H,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC,qIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}